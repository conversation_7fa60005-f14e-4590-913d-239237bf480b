import cv2
import numpy as np
import yaml
import os

# -------------------------- 1. 加载相机内参 --------------------------
def load_camera_params(params_path):
    with open(params_path, "r") as f:
        params = yaml.safe_load(f)
    return (
        np.array(params["camera_matrix"]),
        np.array(params["dist_coeffs"]),
        tuple(params["image_size"])
    )

try:
    camera_matrix, dist_coeffs, img_size = load_camera_params("./camera_params.yml")
except yaml.constructor.ConstructorError:
    # 如果YAML文件包含Python特定的tuple标签，使用unsafe_load
    def load_camera_params_unsafe(params_path):
        with open(params_path, "r") as f:
            params = yaml.unsafe_load(f)
        return (
            np.array(params["camera_matrix"]),
            np.array(params["dist_coeffs"]),
            tuple(params["image_size"]) if isinstance(params["image_size"], list) else params["image_size"]
        )
    camera_matrix, dist_coeffs, img_size = load_camera_params_unsafe("./camera_params.yml")

# -------------------------- 2. 配置参数（需用户调整）--------------------------
CARDBOARD_SIZE = (280, 160)  # 纸板物理尺寸（宽×高，单位：mm，论文参数）
PROJECTOR_RES = (1920, 1080)  # 投影仪分辨率（宽×高）
NUM_CALIB_POINTS = 18  # 校准点数量（≥8，论文建议至少8个）
SAVE_GP_PATH = "./projector_Gp.yml"  # Gp保存路径

# 纸板平面方程（假设初始时纸板在相机坐标系下的平面为 Z = Z0，需手动测量或通过棋盘格校准）
# 这里通过棋盘格预先获取纸板平面：将纸板与棋盘格重合，用solvePnP求平面方程
def get_cardboard_plane():
    # 纸板4个角的世界坐标（纸板坐标系：原点在左上，X向右，Y向下，Z=0）
    cardboard_3d = np.array([
        [0, 0, 0],
        [CARDBOARD_SIZE[0], 0, 0],
        [CARDBOARD_SIZE[0], CARDBOARD_SIZE[1], 0],
        [0, CARDBOARD_SIZE[1], 0]
    ], dtype=np.float32)
    
    # 拍摄纸板图像，手动标记4个角的2D坐标（或自动检测）
    # print("请拍摄纸板图像并标记4个角点（左上→右上→右下→左下）：")
    # cap = cv2.VideoCapture(0)
    # while True:
    #     ret, frame = cap.read()
    #     if not ret: break
    #     cv2.imshow("Mark Cardboard Corners (Press 's' to save)", frame)
    #     if cv2.waitKey(1) == ord('s'):
    #         img_mark = frame.copy()
    #         break
    # cap.release()
    # cv2.destroyAllWindows()
    
    # # 手动标记4个角点（按顺序点击）
    # corners_2d = []
    # def click_event(event, x, y, flags, param):
    #     if event == cv2.EVENT_LBUTTONDOWN and len(corners_2d) < 4:
    #         corners_2d.append((x, y))
    #         cv2.circle(img_mark, (x, y), 5, (0, 255, 0), -1)
    #         cv2.imshow("Corners Marked", img_mark)
    # cv2.imshow("Corners Marked", img_mark)
    # cv2.setMouseCallback("Corners Marked", click_event)
    # while len(corners_2d) < 4:
    #     cv2.waitKey(1)
    # cv2.destroyAllWindows()

    # corners_2d = np.array(corners_2d, dtype=np.float32)

    corners_2d = np.array([[415, 242], [807, 240],[821, 462],[395, 461]], dtype=np.float32)
    
    # 用solvePnP求纸板平面方程 ax + by + cz + d = 0
    ret, rvec, tvec = cv2.solvePnP(cardboard_3d, corners_2d, camera_matrix, dist_coeffs)
    R, _ = cv2.Rodrigues(rvec)  # 旋转矩阵
    # 平面法向量 = R的第三列（纸板坐标系Z轴→相机坐标系）
    n = R[:, 2]
    a, b, c = n
    d = -np.dot(n, tvec.flatten())  # 平面方程：aX + bY + cZ + d = 0
    return np.array([a, b, c, d])

# 获取纸板平面方程
plane_eq = get_cardboard_plane()  # [a, b, c, d]
print(f"纸板平面方程：{plane_eq[0]}X + {plane_eq[1]}Y + {plane_eq[2]}Z + {plane_eq[3]} = 0")

# -------------------------- 3. 投影仪投射十字标记并收集对应点 --------------------------
def project_cross(projector_window_name, x, y, size=50):
    """在投影仪指定位置(Ip_x, Ip_y)投射十字标记"""
    img = np.zeros((PROJECTOR_RES[1], PROJECTOR_RES[0], 3), dtype=np.uint8)
    # 绘制十字
    cv2.line(img, (x, y - size), (x, y + size), (255, 255, 255), 2)
    cv2.line(img, (x - size, y), (x + size, y), (255, 255, 255), 2)
    # 显示在投影仪（假设投影仪为第二屏幕，窗口位置设为投影仪分辨率）
    cv2.namedWindow(projector_window_name, cv2.WINDOW_NORMAL)
    cv2.moveWindow(projector_window_name, PROJECTOR_RES[0], 0)  # 移到第二屏幕
    cv2.imshow(projector_window_name, img)
    cv2.waitKey(1000)  # 等待稳定
    return (x, y)

# 初始化相机和投影仪窗口
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, img_size[0])
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, img_size[1])
projector_window = "Projector Cross"

# 收集 (Pc, Ip) 对应点（Pc：相机坐标系3D点，Ip：投影仪2D点）
pc_list = []  # 3D点：[Xc, Yc, Zc]
ip_list = []  # 2D点：[Up, Vp]（投影仪坐标）

ic_list = [[467, 293], [522, 293], [580, 293], [636, 292], [695, 293], [753, 292],
 [463, 348], [520, 345], [579, 346], [636, 346], [696, 346], [755, 346],
 [459, 403], [518, 403], [576, 403], [636, 403], [697, 403], [757, 403]]

print(f"开始收集{NUM_CALIB_POINTS}个校准点...")
for i in range(NUM_CALIB_POINTS):
    # # 1. 投影仪随机位置投射十字（Ip）
    # ip_x = np.random.randint(200, PROJECTOR_RES[0] - 200)
    # ip_y = np.random.randint(200, PROJECTOR_RES[1] - 200)
    # ip = project_cross(projector_window, ip_x, ip_y)
    # ip_list.append(ip)
    
    # # 2. 相机捕捉图像并检测十字中心（Ic）
    # ret, frame = cap.read()
    # if not ret: break
    # # 畸变矫正
    # frame_undistort = cv2.undistort(frame, camera_matrix, dist_coeffs)
    # gray = cv2.cvtColor(frame_undistort, cv2.COLOR_BGR2GRAY)
    
    # # 检测十字中心（阈值化+轮廓检测）
    # _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    # contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    # if len(contours) == 0:
    #     print(f"第{i+1}个点未检测到十字，跳过...")
    #     continue
    # # 找最大轮廓（十字）
    # cnt = max(contours, key=cv2.contourArea)
    # M = cv2.moments(cnt)
    # if M["m00"] == 0:
    #     print(f"第{i+1}个点十字中心计算失败，跳过...")
    #     continue
    # # 十字中心（亚像素级）
    # ic_x = int(M["m10"] / M["m00"])
    # ic_y = int(M["m01"] / M["m00"])
    # cv2.circle(frame_undistort, (ic_x, ic_y), 5, (0, 0, 255), -1)
    # cv2.imshow("Detected Cross (Press 'c' to confirm)", frame_undistort)
    # if cv2.waitKey(0) != ord('c'):
    #     continue  # 手动确认，避免错误点
    
    ip_x = (i % 6 * 4 + 6) * 54 + 95
    ip_y = (i // 6 * 4 + 5) * 54 + 52
    ip = (ip_x, ip_y)
    ip_list.append(ip)
    ic_x, ic_y = ic_list[i]
    # 3. 根据相机内参和纸板平面方程计算Pc（相机坐标系3D点）
    # 相机内参：fx, fy, cx, cy
    fx, fy = camera_matrix[0, 0], camera_matrix[1, 1]
    cx, cy = camera_matrix[0, 2], camera_matrix[1, 2]
    
    # 由相机投影模型：u = (fx*X + cx*Z)/Z → X = Z*(u - cx)/fx
    #                  v = (fy*Y + cy*Z)/Z → Y = Z*(v - cy)/fy
    # 代入平面方程 aX + bY + cZ + d = 0 → 解Z
    u, v = ic_x, ic_y
    A = plane_eq[0] * (u - cx) / fx + plane_eq[1] * (v - cy) / fy + plane_eq[2]
    B = -plane_eq[3]
    if abs(A) < 1e-6:
        print(f"第{i+1}个点Z计算失败，跳过...")
        continue
    Zc = B / A
    Xc = Zc * (u - cx) / fx
    Yc = Zc * (v - cy) / fy
    Pc = np.array([Xc, Yc, Zc])
    pc_list.append(Pc)
    
    print(f"已收集第{i+1}/{NUM_CALIB_POINTS}个点：Pc={Pc}, Ip={ip}")

# 释放资源
#cap.release()
cv2.destroyAllWindows()

# -------------------------- 4. 求解投影仪投影矩阵Gp（论文2.2）--------------------------
# Gp = [g11 g12 g13 g14; g21 g22 g23 g24; g31 g32 g33 g34]
# 步骤1：解前8个参数（g11-g14, g21-g24）
n_points = len(pc_list)
if n_points < 8:
    raise ValueError("校准点数量不足8个，请重新收集！")

# 构建线性方程组 B*g = 0（论文公式12）
B = []
for Pc, Ip in zip(pc_list, ip_list):
    Xc, Yc, Zc = Pc
    Up, Vp = Ip
    # 公式12：Vp*(Xc*g11 + Yc*g12 + Zc*g13 + g14) - Up*(Xc*g21 + Yc*g22 + Zc*g23 + g24) = 0
    row = [
        Vp * Xc, Vp * Yc, Vp * Zc, Vp,
        -Up * Xc, -Up * Yc, -Up * Zc, -Up
    ]
    B.append(row)
B = np.array(B, dtype=np.float32)

# SVD分解求最小二乘解
U, D, VT = np.linalg.svd(B)
g = VT[-1]  # 最小奇异值对应的特征向量（解）
g11, g12, g13, g14, g21, g22, g23, g24 = g

# 步骤2：解后4个参数（g31-g34，论文公式14）
# 公式14：Up*(Xc*g31 + Yc*g32 + Zc*g33 + g34) = Xc*g11 + Yc*g12 + Zc*g13 + g14
# 取前4个点构建方程组
if n_points < 4:
    raise ValueError("校准点数量不足4个，无法求解g31-g34！")

C = []
D = []
for i in range(4):
    Xc, Yc, Zc = pc_list[i]
    Up, _ = ip_list[i]
    # 方程：Up*Xc*g31 + Up*Yc*g32 + Up*Zc*g33 + Up*g34 = Xc*g11 + Yc*g12 + Zc*g13 + g14
    C.append([Up * Xc, Up * Yc, Up * Zc, Up])
    D.append(Xc * g11 + Yc * g12 + Zc * g13 + g14)
C = np.array(C, dtype=np.float32)
D = np.array(D, dtype=np.float32)

# 最小二乘求解 g3 = [g31, g32, g33, g34]
g3, _, _, _ = np.linalg.lstsq(C, D, rcond=None)
g31, g32, g33, g34 = g3

# 构建完整投影矩阵Gp
Gp = np.array([
    [g11, g12, g13, g14],
    [g21, g22, g23, g24],
    [g31, g32, g33, g34]
], dtype=np.float32)

# 保存Gp到文件
with open(SAVE_GP_PATH, "w") as f:
    yaml.dump({"Gp": Gp.tolist()}, f)

print("投影仪-相机对校准完成！")
print(f"投影矩阵 Gp:\n{Gp}")